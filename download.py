from SmartApi import SmartConnect
import ta
import pyotp,time, math, threading,json, requests, sys, os

from logzero import logger

import pandas as pd
import numpy as np
from datetime import datetime
# from googleapiclient.discovery import build
# from google.oauth2.service_account import Credentials

import warnings
warnings.filterwarnings("ignore")

class AngleOneApi:
    def __init__(self,api,user,pin,totp):
        import os
        from datetime import datetime, timedelta
        folder_name = "data_files"
        os.makedirs(folder_name, exist_ok=True)  # Create folder if it doesn't exist
        current_date = datetime.now().strftime("%Y-%m-%d")
        file_name = f"{folder_name}/token_df_{current_date}.csv"
        try:
            self.token_df = pd.read_csv(file_name)
        except:
          self.url = 'https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json'
          # self.d = requests.get(self.url).json()
          response = requests.get(self.url)
          if response.status_code == 200:
              self.d = response.json()
              self.token_df = pd.DataFrame.from_dict(self.d)
              self.token_df['expiry'] = pd.to_datetime(self.token_df['expiry'])
              self.token_df = self.token_df.astype({'strike': float})
              self.token_df.to_csv(file_name, index=False)
              print("save file")
          else:
              raise Exception("Failed to fetch scrip master data.")
          
        self.obj = SmartConnect(api_key=api)
        self.data = self.obj.generateSession(user, pin, pyotp.TOTP(totp).now())
        self.AUTH_TOKEN = self.data['data']['jwtToken']
        self.refreshToken = self.data['data']['refreshToken']
        self.FEED_TOKEN = self.obj.getfeedToken()
        self.res = self.obj.getProfile(self.refreshToken)
        print(self.res)

        self.order = []


    def get_token(self, name):
      """Fetches the token corresponding to the provided symbol."""
      return self.token_df[self.token_df["symbol"] == name]["token"].iloc[0]
    
    def get_exch_seg(self, name):
       return self.token_df[self.token_df["symbol"] == name]["exch_seg"].iloc[0]
    
    def ltp(self,name):
        return self.obj.ltpData(self.get_exch_seg(name), name, int(self.get_token(name)))['data']['ltp']
    
    def current_his(self,name):
       from datetime import datetime, timedelta
       current_time = datetime.now()
       time_minus_5_day = current_time - timedelta(days=15)

       exchange = self.get_exch_seg(name)
       if name == "NIFTY":
          symboltoken = 99926000
       else:
        symboltoken = str(self.get_token(name))
       interval = "ONE_MINUTE"
       fromdate =time_minus_5_day.strftime("%Y-%m-%d %H:%M")
       todate = current_time.strftime("%Y-%m-%d %H:%M")
       historicParam={
            "exchange": exchange,
            "symboltoken": symboltoken,
            "interval": interval,
            "fromdate": fromdate, 
            "todate": todate
            }
       print(historicParam)

       df = pd.DataFrame(self.obj.getCandleData(historicParam)["data"], columns = ["time", "open","high", "low", "close","volume"])
       return df
    

# def load_credentials(file_path):
#     with open(file_path, 'r') as file:
#         return json.load(file)
    
if 1>0:
#   credentials = load_credentials("kundan.json")
  Api_key = "bAWA7kFJ"
  User_id = "C114755"
  Pin = "5102"
  Totp = "DD2IGQCIHJ6ZP244HCICFTDATA"

broker = AngleOneApi(Api_key, User_id, Pin, Totp)
# sys.exit()
expiry_s= "NIFTY26JUN25"
pe_range = [22950,25500]
ce_range = [23800,26500]
s = 22750
# lispe=[]
# lisce=[]
lis=[]
for i in range(100):
    if pe_range[0]<= s and pe_range[1]>= s:
        lis.append(f"{expiry_s}{s}PE")
        
    if ce_range[0]<= s and ce_range[1]>= s:
        lis.append(f"{expiry_s}{s}CE")

    s+=50
# print(lis)
folder_name = "nifty_"
expiry_folder = f"{folder_name}{expiry_s}"
# Create both the main data directory and the expiry-specific subdirectory
os.makedirs(expiry_folder, exist_ok=True)
print(len(lis))
# sys.exit()

n = 0 
for l in lis:
    print(l)
    df = broker.current_his(l)
    time.sleep(2)
    file_name = f"{expiry_folder}/{l}.csv"
    # print(df)
    df.to_csv(file_name, index=False)
    print(n)    
    time.sleep(2)
    n+=1

