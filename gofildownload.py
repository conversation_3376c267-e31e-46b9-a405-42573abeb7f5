

import requests

url = "https://gofile.io/d/hXUWLb"
response = requests.get(url)

print("Status Code:", response.status_code)
print("Raw Response Text:")
print(response.text)  # This is key to diagnose

# Try JSON parsing only if content is not empty
if response.text.strip():
    try:
        data = response.json()
        print("Parsed JSON:", data)
    except ValueError as e:
        print("Failed to parse JSON:", e)
else:
    print("Response is empty.")
