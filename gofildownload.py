

import requests
import re

def extract_content_id(url):
    """Extract content ID from Gofile.io URL"""
    # Extract ID from URLs like https://gofile.io/d/hXUWLb
    match = re.search(r'/d/([a-zA-Z0-9]+)', url)
    if match:
        return match.group(1)
    return None

def get_gofile_content(content_id):
    """Get content information using Gofile.io API"""
    api_url = f"https://api.gofile.io/getContent?contentId={content_id}"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    try:
        response = requests.get(api_url, headers=headers)
        print(f"API Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            return data
        else:
            print(f"API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return None

    except Exception as e:
        print(f"Error making API request: {e}")
        return None

# Original URL
original_url = "https://gofile.io/d/hXUWLb"
print(f"Original URL: {original_url}")

# Extract content ID
content_id = extract_content_id(original_url)
print(f"Extracted Content ID: {content_id}")

if content_id:
    # Try API approach
    print("\n--- Using Gofile.io API ---")
    api_data = get_gofile_content(content_id)

    if api_data:
        print("API Response:")
        print(api_data)

        # Check if successful and has content
        if api_data.get('status') == 'ok':
            content = api_data.get('data', {}).get('contents', {})
            if content:
                print(f"\nFound {len(content)} items:")
                for item_id, item_info in content.items():
                    print(f"- {item_info.get('name', 'Unknown')} ({item_info.get('type', 'Unknown type')})")
                    if item_info.get('type') == 'file':
                        print(f"  Size: {item_info.get('size', 'Unknown')} bytes")
                        print(f"  Download: {item_info.get('link', 'No link')}")
            else:
                print("No content found in response")
        else:
            print(f"API returned error: {api_data.get('status')}")
    else:
        print("Failed to get data from API")
else:
    print("Could not extract content ID from URL")
