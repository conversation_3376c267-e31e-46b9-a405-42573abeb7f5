

import requests
import re

def extract_content_id(url):
    """Extract content ID from Gofile.io URL"""
    # Extract ID from URLs like https://gofile.io/d/hXUWLb
    match = re.search(r'/d/([a-zA-Z0-9]+)', url)
    if match:
        return match.group(1)
    return None

def get_gofile_content(content_id, token=None):
    """Get content information using Gofile.io API"""
    api_url = f"https://api.gofile.io/getContent?contentId={content_id}"
    if token:
        api_url += f"&token={token}"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    try:
        response = requests.get(api_url, headers=headers)
        print(f"API Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            return data
        else:
            print(f"API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return None

    except Exception as e:
        print(f"Error making API request: {e}")
        return None

def try_web_scraping(url):
    """Try to extract download links from the web page"""
    print("\n--- Trying web scraping approach ---")

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers)
        print(f"Web Status Code: {response.status_code}")

        if response.status_code == 200:
            # Look for potential download links or file information in the HTML
            html_content = response.text

            # Check if it's a redirect or contains JavaScript that loads content
            if 'window.location' in html_content:
                print("Page contains redirect JavaScript")

            # Look for any JSON data embedded in the page
            import json
            json_matches = re.findall(r'({[^}]*"contentId"[^}]*})', html_content)
            if json_matches:
                print("Found potential JSON data in page:")
                for match in json_matches:
                    try:
                        data = json.loads(match)
                        print(f"  {data}")
                    except:
                        print(f"  Raw: {match}")

            # Look for download URLs
            download_links = re.findall(r'https://[^"\s]*gofile[^"\s]*', html_content)
            if download_links:
                print("Found potential download links:")
                for link in set(download_links):  # Remove duplicates
                    print(f"  {link}")

            return html_content
        else:
            print(f"Web request failed: {response.status_code}")
            return None

    except Exception as e:
        print(f"Error in web scraping: {e}")
        return None

# Original URL
original_url = "https://gofile.io/d/hXUWLb"
print(f"Original URL: {original_url}")

# Extract content ID
content_id = extract_content_id(original_url)
print(f"Extracted Content ID: {content_id}")
# hidden lg:inline
if content_id:
    # Try API approach first
    print("\n--- Using Gofile.io API ---")
    api_data = get_gofile_content(content_id)

    success = False
    if api_data:
        print("API Response:")
        print(api_data)

        # Check if successful and has content
        if api_data.get('status') == 'ok':
            content = api_data.get('data', {}).get('contents', {})
            if content:
                print(f"\nFound {len(content)} items:")
                for item_id, item_info in content.items():
                    print(f"- {item_info.get('name', 'Unknown')} ({item_info.get('type', 'Unknown type')})")
                    if item_info.get('type') == 'file':
                        print(f"  Size: {item_info.get('size', 'Unknown')} bytes")
                        print(f"  Download: {item_info.get('link', 'No link')}")
                success = True
            else:
                print("No content found in response")
        else:
            print(f"API returned error: {api_data.get('status')}")

    # If API failed, try web scraping
    if not success:
        print("API approach failed, trying alternative methods...")
        web_content = try_web_scraping(original_url)

        if not web_content:
            print("\n--- Possible reasons for failure ---")
            print("1. The file/folder may have expired or been deleted")
            print("2. The content may require a password or token")
            print("3. The content may be private and require authentication")
            print("4. The Gofile.io service may have changed their API")
            print("\n--- Suggestions ---")
            print("1. Check if the original URL still works in a browser")
            print("2. Contact the person who shared the link")
            print("3. Try accessing the link from the same network/location where it was created")
else:
    print("Could not extract content ID from URL")
